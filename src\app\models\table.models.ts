// table.models.ts
export interface TableColumn {
  name: string;
  displayName: string;
  sortable?: boolean;
  width?: string;
  dataType?: 'text' | 'number' | 'date' | 'boolean';
}

export interface TableAction {
  name: string;
  label: string;
  icon?: string;
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  condition?: (row: any) => boolean;
  hidden?: boolean;
}

export interface TableConfig {
  pageSizeOptions?: number[];
  pageSize?: number;
  selectable?: boolean;
  multiSelect?: boolean;
  emptyMessage?: string;
  striped?: boolean;
  hover?: boolean;
  bordered?: boolean;
  small?: boolean;
  fixed?: boolean;
}
// smart-table.component.ts
import { Component, Input, Output, EventEmitter, ViewChild, SimpleChanges } from '@angular/core';
import { NgModule } from '@angular/core';

import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TableColumn, TableAction, TableConfig } from '../models/table.models';

@Component({
  selector: 'app-smart-table',
  imports: [CommonModule,FormsModule],
  templateUrl: './smart-table.html',
  styleUrls: ['./smart-table.sass']
})
export class SmartTable {
  @Input() columns: TableColumn[] = [];
  @Input() set data(data: any[]) {
    this._data = data || [];
    this.isLoading = false;
    this.updatePaginatedData();
  }
  @Input() actions: TableAction[] = [];
  @Input() showActionsColumn: boolean = true;
  @Input() config: TableConfig = {
    pageSizeOptions: [5, 10, 25, 100],
    pageSize: 10,
    selectable: false,
    multiSelect: false,
    emptyMessage: 'No data available',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    fixed: false
  };
  @Input() isLoading: boolean = false;
  
  @Output() actionClick = new EventEmitter<{action: string, row: any}>();
  @Output() selectionChange = new EventEmitter<any[]>();
  @Output() sortChange = new EventEmitter<{column: string, direction: 'asc' | 'desc'}>();
  @Output() pageChange = new EventEmitter<{page: number, size: number}>();

  _data: any[] = [];
  selectedRows = new Set<any>();
  currentPage = 1;
  pageSize = 10;
  totalPages = 1;
  paginatedData: any[] = [];
  sortColumn = '';
  sortDirection: 'asc' | 'desc' = 'asc';

  get displayedColumns(): string[] {
    let cols = this.columns.map(col => col.name);
    if (this.config.selectable) {
      cols = ['select', ...cols];
    }
    if (this.showActionsColumn && this.visibleActions.length > 0) {
      return [...cols, 'actions'];
    }
    return cols;
  }

  get visibleActions(): TableAction[] {
    return this.actions.filter(action => !action.hidden);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['config']) {
      this.pageSize = this.config.pageSize || 10;
    }
    if (changes['data'] || changes['config']) {
      this.updatePaginatedData();
    }
  }

  updatePaginatedData() {
    // Apply sorting
    let sortedData = [...this._data];
    if (this.sortColumn) {
      sortedData.sort((a, b) => {
        const colConfig = this.columns.find(c => c.name === this.sortColumn);
        const valA = a[this.sortColumn];
        const valB = b[this.sortColumn];

        if (colConfig?.dataType === 'date') {
          const dateA = new Date(valA).getTime();
          const dateB = new Date(valB).getTime();
          return this.sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
        } else if (colConfig?.dataType === 'number') {
          return this.sortDirection === 'asc' ? valA - valB : valB - valA;
        } else {
          // String comparison
          const strA = String(valA).toLowerCase();
          const strB = String(valB).toLowerCase();
          return this.sortDirection === 'asc' 
            ? strA.localeCompare(strB) 
            : strB.localeCompare(strA);
        }
      });
    }

    // Calculate pagination
    this.totalPages = Math.ceil(sortedData.length / this.pageSize);
    const startIndex = (this.currentPage - 1) * this.pageSize;
    this.paginatedData = sortedData.slice(startIndex, startIndex + this.pageSize);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.updatePaginatedData();
    this.pageChange.emit({page, size: this.pageSize});
  }

  onPageSizeChange(size: number) {
    this.pageSize = size;
    this.currentPage = 1;
    this.updatePaginatedData();
    this.pageChange.emit({page: 1, size});
  }

  onSort(column: string) {
    const colConfig = this.columns.find(c => c.name === column);
    if (!colConfig?.sortable) return;

    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }

    this.sortChange.emit({column: this.sortColumn, direction: this.sortDirection});
    this.updatePaginatedData();
  }

  shouldShowAction(action: TableAction, row: any): boolean {
    if (action.hidden) return false;
    if (action.condition) {
      return action.condition(row);
    }
    return true;
  }

  onActionClick(actionName: string, row: any): void {
    this.actionClick.emit({action: actionName, row});
  }

  toggleRowSelection(row: any) {
    if (this.config.multiSelect) {
      if (this.selectedRows.has(row)) {
        this.selectedRows.delete(row);
      } else {
        this.selectedRows.add(row);
      }
    } else {
      this.selectedRows.clear();
      this.selectedRows.add(row);
    }
    this.selectionChange.emit(Array.from(this.selectedRows));
  }

  isSelected(row: any): boolean {
    return this.selectedRows.has(row);
  }

  masterToggle() {
    if (this.isAllSelected()) {
      this.selectedRows.clear();
    } else {
      this.paginatedData.forEach(row => this.selectedRows.add(row));
    }
    this.selectionChange.emit(Array.from(this.selectedRows));
  }

  isAllSelected(): boolean {
    return this.paginatedData.length > 0 && 
           this.selectedRows.size === this.paginatedData.length;
  }

  getSortIcon(column: string): string {
    if (this.sortColumn !== column) return 'cil-sort-slash';
    return this.sortDirection === 'asc' ? 'cil-sort-ascending' : 'cil-sort-descending';
  }

  // Add this method to your SmartTableComponent class
getPages(): number[] {
  const pages: number[] = [];
  const maxVisiblePages = 5; // Adjust this number to show more/fewer page buttons
  
  if (this.totalPages <= maxVisiblePages) {
    // Show all pages if there aren't too many
    for (let i = 1; i <= this.totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Show a window of pages around the current page
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;
    
    if (endPage > this.totalPages) {
      endPage = this.totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // Always show first page
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push(-1); // Use -1 to represent ellipsis
      }
    }
    
    // Add the range of pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Always show last page
    if (endPage < this.totalPages) {
      if (endPage < this.totalPages - 1) {
        pages.push(-1); // Use -1 to represent ellipsis
      }
      pages.push(this.totalPages);
    }
  }
  
  return pages;
}
}
// smart-table.component.sass
.table-container
  position: relative
  min-height: 200px

  &.loading
    opacity: 0.7
    pointer-events: none

  .loading-overlay
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    display: flex
    flex-direction: column
    align-items: center
    justify-content: center
    background: rgba(255, 255, 255, 0.8)
    z-index: 10

    .loading-text
      margin-top: 1rem

  .empty-state
    display: flex
    align-items: center
    padding: 1rem
    gap: 0.5rem

    i
      font-size: 1.5rem

  .table
    margin-bottom: 1rem

    th.sortable
      cursor: pointer
      user-select: none

      &:hover
        background-color: rgba(0, 0, 0, 0.05)

    tr.selected
      background-color: rgba(0, 123, 255, 0.1)

  .action-buttons
    display: flex
    gap: 0.25rem
    flex-wrap: wrap

    .btn
      display: inline-flex
      align-items: center
      gap: 0.25rem

  .pagination
    margin-top: 0

  .table-fixed
    table-layout: fixed
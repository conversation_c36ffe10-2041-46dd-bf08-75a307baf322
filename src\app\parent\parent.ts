// parent.component.ts
import { Component } from '@angular/core';
import { TableConfig } from '../models/table.models';
import { SmartTable } from '../smart-table/smart-table';

@Component({
  selector: 'app-parent',
  templateUrl: './parent.html',
  imports: [SmartTable],
})
export class ParentComponent {
  tableColumns = [
    { name: 'id', displayName: 'ID', sortable: true, dataType: 'number' },
    { name: 'name', displayName: 'Name', sortable: true },
    { name: 'email', displayName: 'Email', sortable: false },
    { name: 'createdAt', displayName: 'Join Date', sortable: true, dataType: 'string' },
    { name: 'active', displayName: 'Active', sortable: true, dataType: 'boolean' }
  ];

  tableData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', createdAt: '2023-01-15', active: true },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', createdAt: '2023-02-20', active: false }
  ];

  tableActions = [
    { 
      name: 'edit', 
      label: 'Edit', 
      icon: 'cil-pencil', 
      color: 'primary',
      hidden: false
    },
    { 
      name: 'delete', 
      label: 'Delete', 
      icon: 'cil-trash', 
      color: 'danger',
      condition: (row: any) => row.active,
      hidden: false
    }
  ];

  tableConfig: TableConfig = {
    pageSizeOptions: [5, 10, 25],
    pageSize: 5,
    selectable: true,
    multiSelect: true,
    emptyMessage: 'No users found',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    fixed: false
  };

  loading = false;

  handleAction(event: {action: string, row: any}): void {
    switch(event.action) {
      case 'edit':
        this.editItem(event.row);
        break;
      case 'delete':
        this.deleteItem(event.row);
        break;
    }
  }

  onSelectionChange(selectedRows: any[]): void {
    console.log('Selected rows:', selectedRows);
  }

  onSortChange(sort: {column: string, direction: 'asc' | 'desc'}): void {
    console.log('Sort changed:', sort);
    // Implement your sorting logic here
  }

  onPageChange(pageEvent: {page: number, size: number}): void {
    console.log('Page changed:', pageEvent);
    // Implement your pagination logic here
  }

  editItem(row: any): void {
    console.log('Edit:', row);
  }

  deleteItem(row: any): void {
    console.log('Delete:', row);
  }
}
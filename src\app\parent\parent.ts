// parent.component.ts
import { Component } from '@angular/core';
import { TableConfig, TableColumn, TableAction } from '../models/table.models';
import { SmartTable } from '../smart-table/smart-table';

@Component({
  selector: 'app-parent',
  templateUrl: './parent.html',
  imports: [SmartTable],
})
export class ParentComponent {
  tableColumns: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true, dataType: 'number' as const },
    { name: 'name', displayName: 'Name', sortable: true, dataType: 'text' as const },
    { name: 'email', displayName: 'Email', sortable: false, dataType: 'text' as const },
    { name: 'createdAt', displayName: 'Join Date', sortable: true, dataType: 'date' as const },
    { name: 'active', displayName: 'Active', sortable: true, dataType: 'boolean' as const }
  ];

  tableData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', createdAt: '2023-01-15', active: true },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', createdAt: '2023-02-20', active: false }
  ];

  tableActions: TableAction[] = [
    {
      name: 'edit',
      label: 'Edit',
      icon: 'cil-pencil',
      color: 'primary' as const,
      hidden: false
    },
    {
      name: 'delete',
      label: 'Delete',
      icon: 'cil-trash',
      color: 'danger' as const,
      condition: (row: any) => row.active,
      hidden: false
    }
  ];

  tableConfig: TableConfig = {
    pageSizeOptions: [5, 10, 25],
    pageSize: 5,
    selectable: true,
    multiSelect: true,
    emptyMessage: 'No users found',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    fixed: false
  };

  loading = false;

  handleAction(event: {action: string, row: any}): void {
    switch(event.action) {
      case 'edit':
        this.editItem(event.row);
        break;
      case 'delete':
        this.deleteItem(event.row);
        break;
    }
  }

  onSelectionChange(selectedRows: any[]): void {
    console.log('Selected rows:', selectedRows);
  }

  onSortChange(sort: {column: string, direction: 'asc' | 'desc'}): void {
    console.log('Sort changed:', sort);
    // Implement your sorting logic here
  }

  onPageChange(pageEvent: {page: number, size: number}): void {
    console.log('Page changed:', pageEvent);
    // Implement your pagination logic here
  }

  editItem(row: any): void {
    console.log('Edit:', row);
  }

  deleteItem(row: any): void {
    console.log('Delete:', row);
  }
}